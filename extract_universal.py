#!/usr/bin/env python3
"""
通用版本：提取markdown表格
适用于各种环境
"""
import json

def extract_table(json_str):
    """提取表格的核心函数"""
    try:
        data = json.loads(json_str)
        
        # 查找markdown_table
        if 'markdown_table' in data:
            return data['markdown_table'].replace('\\n', '\n')
        
        if 'arg1' in data:
            inner_data = json.loads(data['arg1'])
            if 'markdown_table' in inner_data:
                return inner_data['markdown_table'].replace('\\n', '\n')
        
        # 搜索所有字段
        for key, value in data.items():
            if isinstance(value, str):
                try:
                    parsed = json.loads(value)
                    if isinstance(parsed, dict) and 'markdown_table' in parsed:
                        return parsed['markdown_table'].replace('\\n', '\n')
                except:
                    continue
        
        return "错误: 未找到markdown_table字段"
    except Exception as e:
        return f"错误: {e}"

# 使用示例
if __name__ == "__main__":
    # 方法1: 直接在这里修改你的JSON数据
    your_json_data = '''
    {
      "arg1": "{\\"success\\": true, \\"message\\": \\"检测完成\\", \\"total_words\\": 4, \\"markdown_table\\": \\"| 序号 | 敏感词类型 | 敏感词内容 | 出现次数 |\\\\n|------|------------|------------|----------|\\\\n| 1 | 主观词语 | 一般 | 5 |\\\\n| 2 | 主观词语 | 科学 | 1 |\\\\n| 3 | 主观词语 | 合理 | 1 |\\\\n| 4 | 主观词语 | 优秀 | 2 |\\\\n\\", \\"results\\": [{\\"序号\\": 1, \\"敏感词类型\\": \\"主观词语\\", \\"敏感词内容\\": \\"一般\\", \\"出现次数\\": 5}, {\\"序号\\": 2, \\"敏感词类型\\": \\"主观词语\\", \\"敏感词内容\\": \\"科学\\", \\"出现次数\\": 1}, {\\"序号\\": 3, \\"敏感词类型\\": \\"主观词语\\", \\"敏感词内容\\": \\"合理\\", \\"出现次数\\": 1}, {\\"序号\\": 4, \\"敏感词类型\\": \\"主观词语\\", \\"敏感词内容\\": \\"优秀\\", \\"出现次数\\": 2}]}"
    }
    '''
    
    # 方法2: 或者从命令行/stdin读取
    import sys
    if len(sys.argv) > 1:
        your_json_data = sys.argv[1]
    elif not your_json_data.strip():
        your_json_data = input("请输入JSON数据: ")
    
    # 提取并打印表格
    result = extract_table(your_json_data)
    print(result)
