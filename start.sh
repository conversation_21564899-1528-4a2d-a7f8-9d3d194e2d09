#!/bin/bash

echo "启动敏感词检测API服务..."
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.9+"
    exit 1
fi

# 检查是否存在虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "安装依赖包..."
pip install -r requirements.txt

# 检查敏感词文件
if [ ! -f "敏感词列表.xlsx" ]; then
    echo "警告: 未找到敏感词列表.xlsx文件"
    echo "请确保该文件存在于项目根目录"
    read -p "按Enter键继续..."
fi

# 复制环境配置文件
if [ ! -f ".env" ]; then
    echo "创建环境配置文件..."
    cp .env.example .env
fi

# 启动服务
echo
echo "启动API服务..."
echo "服务地址: http://localhost:8087"
echo "API文档: http://localhost:8087/docs"
echo

python main.py
