# 敏感词检测API

基于FastAPI的敏感词检测服务，支持Dify工具集成。

## 功能特性

- 🔍 **高效检测**: 使用Aho-Corasick算法进行快速敏感词匹配
- 📊 **Excel支持**: 从Excel文件读取敏感词数据库
- 📄 **页码追踪**: 支持markdown内容的页码标识和追踪
- 🚀 **高性能**: 内置缓存机制和请求限流
- 🔧 **Dify集成**: 完全兼容Dify工具规范
- 📝 **详细日志**: 使用loguru进行结构化日志记录

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境

复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，根据需要调整配置。

### 3. 准备敏感词文件

确保项目根目录下有 `敏感词列表.xlsx` 文件，包含以下列：
- 敏感词内容列（第一列或包含"敏感词"、"word"、"词汇"、"内容"关键字的列）
- 敏感词类型列（第二列或包含"类型"、"type"、"分类"、"类别"关键字的列）

### 4. 启动服务

```bash
python main.py
```

服务将在 `http://localhost:8087` 启动。

### 5. 访问API文档

- Swagger UI: http://localhost:8087/docs
- ReDoc: http://localhost:8087/redoc

## API接口

### 敏感词检测

**POST** `/detect`

检测markdown内容中的敏感词。

**请求体:**
```json
{
  "content": "# 测试文档\n\n这是一个包含敏感词的测试文档。\n\n<!-- page:1 -->\n\n内容示例..."
}
```

**响应:**
```json
{
  "success": true,
  "message": "检测完成",
  "total_words": 2,
  "markdown_table": "| 序号 | 敏感词类型 | 敏感词内容 | 出现次数 |\n|------|------------|------------|----------|\n| 1 | 政治敏感 | 示例词 | 1 |",
  "results": [
    {
      "序号": 1,
      "敏感词类型": "政治敏感",
      "敏感词内容": "示例词",
      "出现次数": 1
    }
  ]
}
```

### 其他接口

- **GET** `/health` - 健康检查
- **POST** `/reload` - 重新加载敏感词列表
- **GET** `/stats` - 获取敏感词库统计信息

## 页码标识支持

系统支持以下页码标识格式：

```markdown
<!-- page:1 -->
<!-- 第1页 -->
[page:1]
第1页
```

## Dify工具集成

### 1. 导入工具配置

将 `dify_tool_config.json` 文件导入到Dify工具库中。

### 2. 配置API端点

确保Dify能够访问到API服务地址（默认: http://localhost:8087）。

### 3. 使用工具

在Dify工作流中添加"敏感词检测器"工具，输入要检测的markdown内容即可。

## 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| HOST | 0.0.0.0 | 服务器监听地址 |
| PORT | 8087 | 服务器端口 |
| DEBUG | false | 调试模式 |
| LOG_LEVEL | INFO | 日志级别 |
| SENSITIVE_WORDS_FILE | 敏感词列表.xlsx | 敏感词文件路径 |
| CACHE_EXPIRE_SECONDS | 3600 | 缓存过期时间（秒） |
| MAX_CONTENT_LENGTH | 1048576 | 最大内容长度（字节） |
| RATE_LIMIT_REQUESTS | 100 | 限流请求数 |
| RATE_LIMIT_WINDOW | 60 | 限流时间窗口（秒） |

## 性能优化

- **缓存机制**: 相同内容的检测结果会被缓存，避免重复计算
- **Aho-Corasick算法**: 高效的多模式字符串匹配算法
- **请求限流**: 防止API被滥用
- **异步处理**: 基于FastAPI的异步架构

## 日志管理

日志文件位置: `logs/app.log`

- 自动轮转: 文件大小超过10MB时自动轮转
- 保留策略: 保留7天的日志文件
- 压缩存储: 旧日志文件自动压缩为zip格式

## 故障排除

### 常见问题

1. **敏感词文件读取失败**
   - 检查文件路径是否正确
   - 确认Excel文件格式正确
   - 查看日志了解具体错误信息

2. **API响应慢**
   - 检查敏感词数量是否过多
   - 考虑调整缓存配置
   - 查看服务器资源使用情况

3. **Dify集成问题**
   - 确认API服务正常运行
   - 检查网络连接
   - 验证工具配置文件格式

## 开发说明

### 项目结构

```
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用主文件
│   ├── config.py            # 配置管理
│   ├── models.py            # 数据模型
│   ├── sensitive_word_detector.py  # 敏感词检测核心
│   ├── cache.py             # 缓存管理
│   └── logger_config.py     # 日志配置
├── logs/                    # 日志目录
├── main.py                  # 启动脚本
├── requirements.txt         # 依赖列表
├── .env.example            # 环境配置示例
├── dify_tool_config.json   # Dify工具配置
├── openapi_schema.json     # OpenAPI规范
├── 敏感词列表.xlsx         # 敏感词数据库
└── README.md               # 项目文档
```

### 扩展开发

1. **添加新的检测算法**: 在 `sensitive_word_detector.py` 中实现新的检测方法
2. **自定义缓存策略**: 修改 `cache.py` 中的缓存逻辑
3. **增加新的API接口**: 在 `main.py` 中添加新的路由

## 许可证

本项目采用MIT许可证。
