#!/usr/bin/env python3
"""
可自定义字段名的markdown表格提取器
"""
import json
import sys
import argparse


def extract_table_from_field(input_str, field_name="arg1"):
    """从指定字段中提取表格"""
    try:
        # 解析外层JSON
        outer_data = json.loads(input_str)
        
        # 获取指定字段
        if field_name not in outer_data:
            return f"错误: 未找到字段 '{field_name}'"
        
        # 解析字段中的JSON字符串
        inner_data = json.loads(outer_data[field_name])
        
        # 提取markdown_table
        if 'markdown_table' not in inner_data:
            return "错误: 未找到markdown_table字段"
        
        # 格式化表格
        table = inner_data['markdown_table'].replace('\\n', '\n')
        return table
        
    except json.JSONDecodeError as e:
        return f"JSON解析错误: {e}"
    except Exception as e:
        return f"处理错误: {e}"


def main():
    parser = argparse.ArgumentParser(description='从指定字段中提取markdown表格')
    parser.add_argument('json_input', help='JSON格式的输入数据')
    parser.add_argument('-f', '--field', default='arg1', help='包含数据的字段名 (默认: arg1)')
    
    args = parser.parse_args()
    
    result = extract_table_from_field(args.json_input, args.field)
    print(result)


if __name__ == "__main__":
    main()
