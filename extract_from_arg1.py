#!/usr/bin/env python3
"""
专门处理arg1格式的数据
"""
import json
import sys

def extract_table_from_arg1(input_str):
    """从arg1格式的数据中提取表格"""
    try:
        # 解析外层JSON
        outer_data = json.loads(input_str)
        
        # 获取arg1字段
        if 'arg1' not in outer_data:
            return "错误: 未找到arg1字段"
        
        # 解析arg1中的JSON字符串
        inner_data = json.loads(outer_data['arg1'])
        
        # 提取markdown_table
        if 'markdown_table' not in inner_data:
            return "错误: 未找到markdown_table字段"
        
        # 格式化表格
        table = inner_data['markdown_table'].replace('\\n', '\n')
        return table
        
    except json.JSONDecodeError as e:
        return f"JSON解析错误: {e}"
    except Exception as e:
        return f"处理错误: {e}"

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("使用方法: python extract_from_arg1.py 'JSON字符串'")
        sys.exit(1)
    
    result = extract_table_from_arg1(sys.argv[1])
    print(result)
