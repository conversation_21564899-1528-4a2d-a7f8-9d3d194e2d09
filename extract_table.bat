@echo off
REM 提取markdown表格的批处理脚本
REM 使用方法: extract_table.bat "JSON字符串"
REM 或者: extract_table.bat < input.json

if "%~1"=="" (
    echo 使用方法: extract_table.bat "JSON字符串"
    echo 或者从文件读取: extract_table.bat -f filename.json
    echo 或者从管道读取: type data.json ^| extract_table.bat
    exit /b 1
)

if "%~1"=="-f" (
    python extract_markdown_table.py -f "%~2"
) else (
    echo %~1 | python extract_table_oneliner.py
)
