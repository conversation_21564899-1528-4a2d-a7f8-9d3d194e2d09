"""
FastAPI敏感词检测应用程序主文件
"""

import os
import time
from typing import Dict, Any
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from loguru import logger

from app.config import get_settings
from app.models import SensitiveWordRequest, DetectionResponse, ErrorResponse
from app.sensitive_word_detector import SensitiveWordDetector
from app.logger_config import setup_logger

# 初始化设置和日志
settings = get_settings()
setup_logger()

# 创建限流器
limiter = Limiter(key_func=get_remote_address, default_limits=["200/minute"])

# 创建FastAPI应用
app = FastAPI(
    title="敏感词检测API",
    description="基于FastAPI的敏感词检测服务，支持Dify工具集成",
    version=settings.dify_tool_version,
    docs_url="/docs",
    redoc_url="/redoc",
)

# 添加限流中间件
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局敏感词检测器实例
detector = None


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global detector
    logger.info("正在启动敏感词检测服务...")

    # 创建日志目录
    os.makedirs("logs", exist_ok=True)

    # 初始化敏感词检测器
    detector = SensitiveWordDetector()

    logger.info("敏感词检测服务启动完成")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("敏感词检测服务正在关闭...")


def get_detector() -> SensitiveWordDetector:
    """获取敏感词检测器实例"""
    if detector is None:
        raise HTTPException(status_code=500, detail="敏感词检测器未初始化")
    return detector


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    start_time = time.time()

    # 记录请求信息
    logger.info(f"请求开始: {request.method} {request.url}")

    response = await call_next(request)

    # 记录响应信息
    process_time = time.time() - start_time
    logger.info(
        f"请求完成: {request.method} {request.url} - 状态码: {response.status_code} - 耗时: {process_time:.3f}s"
    )

    return response


@app.get("/", response_model=Dict[str, Any])
async def root():
    """根路径，返回API信息"""
    return {
        "name": "敏感词检测API",
        "version": settings.dify_tool_version,
        "description": "基于FastAPI的敏感词检测服务",
        "docs_url": "/docs",
        "health_check": "/health",
    }


@app.get("/health", response_model=Dict[str, Any])
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "service": "sensitive_word_detector",
    }


@app.post(
    "/detect",
    response_model=DetectionResponse,
    responses={
        400: {"model": ErrorResponse, "description": "请求参数错误"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"},
    },
)
@limiter.limit(f"{settings.rate_limit_requests}/{settings.rate_limit_window}minute")
async def detect_sensitive_words(
    request: Request,
    data: SensitiveWordRequest,
    detector: SensitiveWordDetector = Depends(get_detector),
):
    """
    敏感词检测接口

    - **content**: 要检测的markdown内容

    返回检测结果，包括markdown格式的表格和详细的结果列表
    """
    try:
        # 验证内容长度
        if len(data.content) > settings.max_content_length:
            raise HTTPException(
                status_code=400,
                detail=f"内容长度超过限制 ({settings.max_content_length} 字符)",
            )

        # 执行敏感词检测
        logger.info(f"开始检测敏感词，内容长度: {len(data.content)}")
        results = detector.detect_sensitive_words(data.content)

        # 生成markdown表格
        markdown_table = detector.generate_markdown_table(results)

        # 构建响应
        response = DetectionResponse(
            success=True,
            message="检测完成",
            total_words=len(results),
            markdown_table=markdown_table,
            results=results,
        )

        logger.info(f"敏感词检测完成，发现 {len(results)} 个敏感词")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"敏感词检测失败: {e}")
        raise HTTPException(status_code=500, detail=f"检测失败: {str(e)}")


@app.post("/reload", response_model=Dict[str, Any])
async def reload_sensitive_words(
    detector: SensitiveWordDetector = Depends(get_detector),
):
    """
    重新加载敏感词列表

    当敏感词Excel文件更新后，可以调用此接口重新加载
    """
    try:
        success = detector.reload_sensitive_words()
        if success:
            return {
                "success": True,
                "message": "敏感词列表重新加载成功",
                "timestamp": time.time(),
            }
        else:
            raise HTTPException(status_code=500, detail="重新加载失败")
    except Exception as e:
        logger.error(f"重新加载敏感词列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"重新加载失败: {str(e)}")


@app.get("/stats", response_model=Dict[str, Any])
async def get_stats(detector: SensitiveWordDetector = Depends(get_detector)):
    """
    获取敏感词库统计信息
    """
    try:
        total_words = len(detector.sensitive_words)
        word_types = {}

        for word, word_type in detector.sensitive_words.items():
            if word_type not in word_types:
                word_types[word_type] = 0
            word_types[word_type] += 1

        return {
            "total_words": total_words,
            "word_types": word_types,
            "automaton_ready": detector.automaton is not None,
            "timestamp": time.time(),
        }
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


# 异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.warning(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            success=False, message=exc.detail, error_code=str(exc.status_code)
        ).dict(),
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            success=False, message="服务器内部错误", error_code="500"
        ).dict(),
    )
