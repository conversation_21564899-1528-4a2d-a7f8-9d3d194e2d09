#!/usr/bin/env python3
"""
最简单版本：直接从stdin读取或作为字符串处理
"""
import json
import sys

def extract_table(input_str):
    """提取表格"""
    try:
        # 解析JSON
        data = json.loads(input_str)
        
        # 查找markdown_table
        if 'markdown_table' in data:
            return data['markdown_table'].replace('\\n', '\n')
        
        # 在arg1中查找
        if 'arg1' in data:
            inner_data = json.loads(data['arg1'])
            if 'markdown_table' in inner_data:
                return inner_data['markdown_table'].replace('\\n', '\n')
        
        # 在所有字段中搜索
        for key, value in data.items():
            if isinstance(value, str):
                try:
                    parsed = json.loads(value)
                    if isinstance(parsed, dict) and 'markdown_table' in parsed:
                        return parsed['markdown_table'].replace('\\n', '\n')
                except:
                    continue
        
        return "错误: 未找到markdown_table字段"
        
    except Exception as e:
        return f"错误: {e}"

# 直接从stdin读取
if __name__ == "__main__":
    input_data = sys.stdin.read().strip()
    if input_data:
        result = extract_table(input_data)
        print(result)
    else:
        print("请通过stdin提供JSON数据")
