#!/bin/bash

# 敏感词检测API 手动部署脚本（不使用Docker）
# 使用方法: ./deploy-manual.sh

set -e

echo "🚀 开始手动部署敏感词检测API..."

# 检查Python版本
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+' | head -1)
required_version="3.9"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python版本过低，需要Python 3.9+，当前版本: $python_version"
    exit 1
fi

echo "✅ Python版本检查通过: $python_version"

# 安装系统依赖
echo "📦 安装系统依赖..."
if command -v apt-get &> /dev/null; then
    sudo apt-get update
    sudo apt-get install -y python3-pip python3-venv gcc g++ curl
elif command -v yum &> /dev/null; then
    sudo yum install -y python3-pip gcc gcc-c++ curl
elif command -v dnf &> /dev/null; then
    sudo dnf install -y python3-pip gcc gcc-c++ curl
else
    echo "⚠️  无法自动安装系统依赖，请手动安装: python3-pip, gcc, g++, curl"
fi

# 创建虚拟环境
echo "🔧 创建Python虚拟环境..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "⬆️  升级pip..."
pip install --upgrade pip

# 安装依赖
echo "📚 安装Python依赖..."
pip install -r requirements.txt

# 检查必要文件
echo "📋 检查必要文件..."
if [ ! -f "sensitive_words.xlsx" ]; then
    echo "❌ 缺少敏感词文件: sensitive_words.xlsx"
    exit 1
fi

# 创建.env文件
if [ ! -f ".env" ]; then
    echo "📝 创建.env文件..."
    cp .env.example .env
fi

# 创建日志目录
mkdir -p logs

# 创建systemd服务文件
echo "🔧 创建systemd服务..."
SERVICE_FILE="/etc/systemd/system/sensitive-word-api.service"
CURRENT_DIR=$(pwd)
USER=$(whoami)

sudo tee $SERVICE_FILE > /dev/null <<EOF
[Unit]
Description=Sensitive Word Detection API
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$CURRENT_DIR
Environment=PATH=$CURRENT_DIR/venv/bin
ExecStart=$CURRENT_DIR/venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
sudo systemctl daemon-reload

# 启用并启动服务
echo "🚀 启动服务..."
sudo systemctl enable sensitive-word-api
sudo systemctl start sensitive-word-api

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务状态
if sudo systemctl is-active --quiet sensitive-word-api; then
    echo "✅ 服务启动成功！"
    
    # 健康检查
    if curl -f http://localhost:8087/health &>/dev/null; then
        echo "✅ API健康检查通过！"
        echo "📊 API文档: http://localhost:8087/docs"
        echo "🔍 健康检查: http://localhost:8087/health"
        echo "📈 统计信息: http://localhost:8087/stats"
    else
        echo "⚠️  API健康检查失败，请检查日志"
    fi
else
    echo "❌ 服务启动失败，请检查日志:"
    sudo systemctl status sensitive-word-api
    exit 1
fi

echo "🎉 部署完成！"
echo ""
echo "常用命令:"
echo "  查看状态: sudo systemctl status sensitive-word-api"
echo "  查看日志: sudo journalctl -u sensitive-word-api -f"
echo "  重启服务: sudo systemctl restart sensitive-word-api"
echo "  停止服务: sudo systemctl stop sensitive-word-api"
echo "  禁用服务: sudo systemctl disable sensitive-word-api"
