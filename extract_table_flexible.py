#!/usr/bin/env python3
"""
灵活版本：提取markdown表格内容
支持多种输入格式
"""
import json
import sys


def extract_markdown_table_flexible(data):
    """
    灵活提取markdown表格内容
    支持多种数据结构
    """
    try:
        # 如果是字符串，先解析
        if isinstance(data, str):
            data = json.loads(data)
        
        # 情况1: 直接包含markdown_table字段
        if isinstance(data, dict) and 'markdown_table' in data:
            return data['markdown_table'].replace('\\n', '\n')
        
        # 情况2: 数据在arg1字段中
        if isinstance(data, dict) and 'arg1' in data:
            arg1_data = data['arg1']
            if isinstance(arg1_data, str):
                arg1_data = json.loads(arg1_data)
            if isinstance(arg1_data, dict) and 'markdown_table' in arg1_data:
                return arg1_data['markdown_table'].replace('\\n', '\n')
        
        # 情况3: 尝试在所有字段中查找
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, str):
                    try:
                        parsed_value = json.loads(value)
                        if isinstance(parsed_value, dict) and 'markdown_table' in parsed_value:
                            return parsed_value['markdown_table'].replace('\\n', '\n')
                    except:
                        continue
                elif isinstance(value, dict) and 'markdown_table' in value:
                    return value['markdown_table'].replace('\\n', '\n')
        
        return "错误: 未找到markdown_table字段"
        
    except json.JSONDecodeError as e:
        return f"JSON解析错误: {e}"
    except Exception as e:
        return f"处理错误: {e}"


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python extract_table_flexible.py 'JSON字符串'")
        print("  echo 'JSON字符串' | python extract_table_flexible.py")
        print("  python extract_table_flexible.py < file.json")
        sys.exit(1)
    
    # 获取输入
    if len(sys.argv) > 1:
        input_data = sys.argv[1]
    else:
        input_data = sys.stdin.read()
    
    # 提取并输出表格
    result = extract_markdown_table_flexible(input_data)
    print(result)


if __name__ == "__main__":
    main()
