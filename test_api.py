"""
API测试脚本
"""

import requests
import json
import time


def test_api():
    """测试API功能"""
    base_url = "http://localhost:8087"

    # 测试健康检查
    print("1. 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
    except Exception as e:
        print(f"健康检查失败: {e}")
        return

    # 测试统计信息
    print("2. 测试统计信息...")
    try:
        response = requests.get(f"{base_url}/stats")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
    except Exception as e:
        print(f"获取统计信息失败: {e}")

    # 测试敏感词检测
    print("3. 测试敏感词检测...")
    test_content = """
# 测试文档

这是一个测试文档，用于验证敏感词检测功能。

<!-- page:1 -->

文档内容包含一些示例文字，比如Windows系统。

<!-- page:2 -->

第二页的内容，可能包含更多测试内容，包括美的空调和海尔冰箱。
"""

    try:
        response = requests.post(
            f"{base_url}/detect",
            json={"content": test_content},
            headers={"Content-Type": "application/json"},
        )
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"检测成功: {result.get('success')}")
        print(f"检测到敏感词数量: {result.get('total_words')}")
        print(f"Markdown表格:")
        print(result.get("markdown_table"))
        print()

        if result.get("results"):
            print("详细结果:")
            for item in result["results"]:
                print(f"  - {item}")
        print()

    except Exception as e:
        print(f"敏感词检测失败: {e}")

    # 测试缓存功能（重复相同请求）
    print("4. 测试缓存功能（重复请求）...")
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/detect",
            json={"content": test_content},
            headers={"Content-Type": "application/json"},
        )
        end_time = time.time()
        print(f"第二次请求耗时: {end_time - start_time:.3f}秒")
        print(f"状态码: {response.status_code}")
        print()
    except Exception as e:
        print(f"缓存测试失败: {e}")

    # 测试重新加载敏感词
    print("5. 测试重新加载敏感词...")
    try:
        response = requests.post(f"{base_url}/reload")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
    except Exception as e:
        print(f"重新加载失败: {e}")


if __name__ == "__main__":
    print("开始测试敏感词检测API...")
    print("请确保API服务已启动 (python main.py)")
    print("=" * 50)
    test_api()
    print("=" * 50)
    print("测试完成！")
