"""
数据模型定义
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class SensitiveWordRequest(BaseModel):
    """敏感词检测请求模型"""

    content: str = Field(..., description="要检测的markdown内容", max_length=1048576)

    model_config = {
        "json_schema_extra": {
            "example": {
                "content": "# 测试文档\n\n这是一个包含敏感词的测试文档。\n\n<!-- page:1 -->\n\n内容示例..."
            }
        }
    }


class SensitiveWordResult(BaseModel):
    """敏感词检测结果"""

    序号: int = Field(..., description="序号")
    敏感词类型: str = Field(..., description="敏感词类型")
    敏感词内容: str = Field(..., description="敏感词内容")
    出现次数: int = Field(..., description="出现次数")


class DetectionResponse(BaseModel):
    """检测响应模型"""

    success: bool = Field(True, description="检测是否成功")
    message: str = Field("检测完成", description="响应消息")
    total_words: int = Field(..., description="检测到的敏感词总数")
    markdown_table: str = Field(..., description="markdown格式的检测结果表格")
    results: List[SensitiveWordResult] = Field(..., description="详细检测结果")

    model_config = {
        "json_schema_extra": {
            "example": {
                "success": True,
                "message": "检测完成",
                "total_words": 2,
                "markdown_table": "| 序号 | 敏感词类型 | 敏感词内容 | 出现次数 |\n|------|------------|------------|----------|\n| 1 | 政治敏感 | 示例词 | 1 |",
                "results": [
                    {
                        "序号": 1,
                        "敏感词类型": "政治敏感",
                        "敏感词内容": "示例词",
                        "出现次数": 1,
                    }
                ],
            }
        }
    }


class ErrorResponse(BaseModel):
    """错误响应模型"""

    success: bool = Field(False, description="操作是否成功")
    message: str = Field(..., description="错误消息")
    error_code: Optional[str] = Field(None, description="错误代码")
