#!/usr/bin/env python3
"""
从文件读取并提取表格
"""
import json
import sys

def extract_table_from_arg1(data):
    """从arg1格式的数据中提取表格"""
    try:
        # 如果是字符串，解析为JSON
        if isinstance(data, str):
            data = json.loads(data)
        
        # 获取arg1字段
        if 'arg1' not in data:
            return "错误: 未找到arg1字段"
        
        # 解析arg1中的JSON字符串
        inner_data = json.loads(data['arg1'])
        
        # 提取markdown_table
        if 'markdown_table' not in inner_data:
            return "错误: 未找到markdown_table字段"
        
        # 格式化表格
        table = inner_data['markdown_table'].replace('\\n', '\n')
        return table
        
    except json.JSONDecodeError as e:
        return f"JSON解析错误: {e}"
    except Exception as e:
        return f"处理错误: {e}"

def main():
    if len(sys.argv) == 2:
        # 从文件读取
        filename = sys.argv[1]
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = f.read()
        except FileNotFoundError:
            print(f"错误: 文件 {filename} 不存在")
            sys.exit(1)
        except Exception as e:
            print(f"读取文件错误: {e}")
            sys.exit(1)
    else:
        # 从stdin读取
        data = sys.stdin.read()
    
    if not data.strip():
        print("错误: 没有输入数据")
        sys.exit(1)
    
    result = extract_table_from_arg1(data)
    print(result)

if __name__ == "__main__":
    main()
