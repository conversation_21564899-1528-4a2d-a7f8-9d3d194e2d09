#!/usr/bin/env python3
"""
简单版本：可自定义字段名
使用方法: python extract_simple_custom.py "JSON字符串" [字段名]
"""
import json
import sys

def extract_table(json_str, field_name="arg1"):
    """提取表格"""
    try:
        # 解析外层JSON
        data = json.loads(json_str)
        
        # 获取指定字段的数据
        if field_name in data:
            inner_json = json.loads(data[field_name])
            if 'markdown_table' in inner_json:
                return inner_json['markdown_table'].replace('\\n', '\n')
        
        return f"错误: 未找到字段 '{field_name}' 或其中的 'markdown_table'"
        
    except Exception as e:
        return f"错误: {e}"

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("使用方法: python extract_simple_custom.py \"JSON字符串\" [字段名]")
        print("默认字段名: arg1")
        sys.exit(1)
    
    json_input = sys.argv[1]
    field_name = sys.argv[2] if len(sys.argv) > 2 else "arg1"
    
    result = extract_table(json_input, field_name)
    print(result)
