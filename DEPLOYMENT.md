# Linux服务器部署指南

## 🎯 部署方案对比

| 特性 | Docker部署 | 手动部署 |
|------|------------|----------|
| **环境一致性** | ✅ 完全一致 | ⚠️ 依赖系统环境 |
| **部署难度** | ✅ 简单 | ⚠️ 中等 |
| **资源占用** | ⚠️ 稍高 | ✅ 较低 |
| **维护性** | ✅ 优秀 | ⚠️ 一般 |
| **扩展性** | ✅ 优秀 | ❌ 困难 |
| **隔离性** | ✅ 完全隔离 | ❌ 无隔离 |
| **推荐指数** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

## 🐳 方案一：Docker部署（推荐）

### 前置要求
- Linux服务器（Ubuntu 18.04+, CentOS 7+）
- Docker 20.10+
- Docker Compose 1.29+
- 至少1GB内存

### 快速部署

1. **上传项目文件到服务器**
```bash
# 方法1: 使用scp
scp -r /path/to/jianchazhushou user@server:/opt/

# 方法2: 使用git
git clone <your-repo> /opt/jianchazhushou
```

2. **安装Docker（如果未安装）**
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y docker docker-compose
sudo systemctl enable docker
sudo systemctl start docker
```

3. **执行部署脚本**
```bash
cd /opt/jianchazhushou
chmod +x deploy.sh
./deploy.sh
```

### 手动Docker部署

```bash
# 1. 进入项目目录
cd /opt/jianchazhushou

# 2. 确保敏感词文件存在
ls -la sensitive_words.xlsx

# 3. 配置环境变量
cp .env.example .env
# 编辑.env文件，调整配置

# 4. 构建并启动
docker-compose up -d

# 5. 检查状态
docker-compose ps
curl http://localhost:8087/health
```

### Docker管理命令

```bash
# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 更新代码后重新部署
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# 进入容器调试
docker-compose exec sensitive-word-api bash
```

## 🔧 方案二：手动部署

### 前置要求
- Linux服务器
- Python 3.9+
- gcc/g++编译器
- sudo权限

### 快速部署

```bash
# 1. 上传项目文件
scp -r /path/to/jianchazhushou user@server:/opt/

# 2. 执行部署脚本
cd /opt/jianchazhushou
chmod +x deploy-manual.sh
./deploy-manual.sh
```

### 手动步骤

```bash
# 1. 安装系统依赖
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y python3-pip python3-venv gcc g++ curl

# CentOS/RHEL
sudo yum install -y python3-pip gcc gcc-c++ curl

# 2. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 3. 安装Python依赖
pip install --upgrade pip
pip install -r requirements.txt

# 4. 配置环境
cp .env.example .env
mkdir -p logs

# 5. 测试运行
python main.py

# 6. 配置systemd服务（生产环境）
sudo cp sensitive-word-api.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable sensitive-word-api
sudo systemctl start sensitive-word-api
```

## 🔒 安全配置

### 防火墙设置
```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 8087/tcp

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=8087/tcp
sudo firewall-cmd --reload
```

### Nginx反向代理（可选）
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8087;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 监控和维护

### 日志查看
```bash
# Docker部署
docker-compose logs -f

# 手动部署
sudo journalctl -u sensitive-word-api -f
tail -f logs/app.log
```

### 性能监控
```bash
# 检查API状态
curl http://localhost:8087/health

# 查看统计信息
curl http://localhost:8087/stats

# 系统资源监控
htop
docker stats  # Docker部署
```

### 备份和恢复
```bash
# 备份敏感词文件
cp sensitive_words.xlsx sensitive_words.xlsx.backup

# 备份配置
cp .env .env.backup

# 重新加载敏感词（无需重启）
curl -X POST http://localhost:8087/reload
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
sudo netstat -tlnp | grep 8087
# 修改.env中的PORT配置
```

2. **权限问题**
```bash
# 检查文件权限
ls -la sensitive_words.xlsx
chmod 644 sensitive_words.xlsx
```

3. **内存不足**
```bash
# 检查内存使用
free -h
# 考虑增加swap或升级服务器
```

4. **中文编码问题**
```bash
# 检查系统编码
locale
# 设置UTF-8编码
export LANG=C.UTF-8
export LC_ALL=C.UTF-8
```

## 📈 性能优化

### 生产环境建议
- 使用Nginx反向代理
- 配置SSL证书
- 设置日志轮转
- 监控系统资源
- 定期备份数据

### 扩展部署
```bash
# 多实例部署（Docker）
docker-compose up --scale sensitive-word-api=3

# 负载均衡配置
# 使用Nginx upstream配置多个后端
```

## 🎉 总结

**推荐使用Docker部署**，因为：
1. 环境一致性好，避免依赖问题
2. 部署简单，一键完成
3. 易于维护和扩展
4. 生产环境更稳定

如果服务器资源有限或不支持Docker，可以选择手动部署方案。
