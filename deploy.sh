#!/bin/bash

# 敏感词检测API Docker部署脚本
# 使用方法: ./deploy.sh

set -e

echo "🚀 开始部署敏感词检测API..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    echo "安装命令: curl -fsSL https://get.docker.com | sh"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查必要文件
echo "📋 检查必要文件..."
required_files=("Dockerfile" "docker-compose.yml" "requirements.txt" "sensitive_words.xlsx")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

# 检查.env文件
if [ ! -f ".env" ]; then
    echo "📝 创建.env文件..."
    cp .env.example .env
    echo "✅ 已创建.env文件，请根据需要修改配置"
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down 2>/dev/null || true

# 构建镜像
echo "🔨 构建Docker镜像..."
docker-compose build --no-cache

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 健康检查
echo "🔍 检查服务状态..."
if curl -f http://localhost:8087/health &>/dev/null; then
    echo "✅ 服务启动成功！"
    echo "📊 API文档: http://localhost:8087/docs"
    echo "🔍 健康检查: http://localhost:8087/health"
    echo "📈 统计信息: http://localhost:8087/stats"
else
    echo "❌ 服务启动失败，请检查日志:"
    docker-compose logs
    exit 1
fi

# 显示容器状态
echo "📦 容器状态:"
docker-compose ps

echo "🎉 部署完成！"
echo ""
echo "常用命令:"
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo "  更新服务: ./deploy.sh"
