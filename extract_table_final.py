#!/usr/bin/env python3
"""
最终版本：提取markdown表格
支持多种输入方式
"""
import json
import sys
import os

def extract_table(input_data):
    """提取表格的核心函数"""
    try:
        # 解析JSON
        if isinstance(input_data, str):
            data = json.loads(input_data)
        else:
            data = input_data
        
        # 查找markdown_table
        # 情况1: 直接在根级别
        if 'markdown_table' in data:
            return data['markdown_table'].replace('\\n', '\n')
        
        # 情况2: 在arg1字段中
        if 'arg1' in data:
            inner_data = json.loads(data['arg1'])
            if 'markdown_table' in inner_data:
                return inner_data['markdown_table'].replace('\\n', '\n')
        
        # 情况3: 在其他字段中搜索
        for key, value in data.items():
            if isinstance(value, str):
                try:
                    parsed = json.loads(value)
                    if isinstance(parsed, dict) and 'markdown_table' in parsed:
                        return parsed['markdown_table'].replace('\\n', '\n')
                except:
                    continue
        
        return "错误: 未找到markdown_table字段"
        
    except Exception as e:
        return f"错误: {e}"

def main():
    """主函数"""
    # 获取输入数据
    input_data = None
    
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        # 检查是否是文件
        if os.path.isfile(arg):
            with open(arg, 'r', encoding='utf-8') as f:
                input_data = f.read()
        else:
            # 作为JSON字符串处理
            input_data = arg
    else:
        # 从stdin读取
        input_data = sys.stdin.read()
    
    if not input_data or not input_data.strip():
        print("使用方法:")
        print("  python extract_table_final.py 'JSON字符串'")
        print("  python extract_table_final.py filename.json")
        print("  echo 'JSON字符串' | python extract_table_final.py")
        sys.exit(1)
    
    # 提取并输出表格
    result = extract_table(input_data)
    print(result)

if __name__ == "__main__":
    main()
