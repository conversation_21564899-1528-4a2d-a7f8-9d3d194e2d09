{"openapi": "3.0.2", "info": {"title": "敏感词检测API", "description": "基于FastAPI的敏感词检测服务，支持Dify工具集成", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8087", "description": "本地开发服务器"}], "paths": {"/detect": {"post": {"summary": "敏感词检测", "description": "检测markdown内容中的敏感词并返回结果表格", "operationId": "detect_sensitive_words", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SensitiveWordRequest"}}}}, "responses": {"200": {"description": "检测成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetectionResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/health": {"get": {"summary": "健康检查", "description": "检查服务状态", "operationId": "health_check", "responses": {"200": {"description": "服务正常", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}, "timestamp": {"type": "number"}, "service": {"type": "string"}}}}}}}}}}, "components": {"schemas": {"SensitiveWordRequest": {"type": "object", "required": ["content"], "properties": {"content": {"type": "string", "description": "要检测的markdown内容", "maxLength": 1048576, "example": "# 测试文档\n\n这是一个包含敏感词的测试文档。\n\n<!-- page:1 -->\n\n内容示例..."}}}, "SensitiveWordResult": {"type": "object", "properties": {"序号": {"type": "integer", "description": "序号"}, "敏感词类型": {"type": "string", "description": "敏感词类型"}, "敏感词内容": {"type": "string", "description": "敏感词内容"}, "出现次数": {"type": "integer", "description": "出现次数"}}}, "DetectionResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "检测是否成功", "default": true}, "message": {"type": "string", "description": "响应消息", "default": "检测完成"}, "total_words": {"type": "integer", "description": "检测到的敏感词总数"}, "markdown_table": {"type": "string", "description": "markdown格式的检测结果表格"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/SensitiveWordResult"}, "description": "详细检测结果"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "default": false}, "message": {"type": "string", "description": "错误消息"}, "error_code": {"type": "string", "description": "错误代码"}}}}}}