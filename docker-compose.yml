version: '3.8'

services:
  sensitive-word-api:
    build: .
    container_name: sensitive-word-detector
    ports:
      - "8087:8087"
    volumes:
      - ./sensitive_words.xlsx:/app/sensitive_words.xlsx:ro
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    environment:
      - HOST=0.0.0.0
      - PORT=8087
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8087/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - sensitive-word-net

networks:
  sensitive-word-net:
    driver: bridge
