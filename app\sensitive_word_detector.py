"""
敏感词检测核心模块
"""

import re
import os
import pandas as pd
import ahocorasick
from typing import Dict, List
from collections import defaultdict
from loguru import logger
from app.config import get_settings
from app.models import SensitiveWordResult
from app.cache import detection_cache


class SensitiveWordDetector:
    """敏感词检测器"""

    def __init__(self):
        self.settings = get_settings()
        self.sensitive_words: Dict[str, str] = {}  # 敏感词 -> 类型
        self.automaton = None
        self._load_sensitive_words()
        self._build_automaton()

    def _load_sensitive_words(self) -> None:
        """从Excel文件加载敏感词列表"""
        try:
            file_path = self.settings.sensitive_words_file
            if not os.path.exists(file_path):
                logger.error(f"敏感词文件不存在: {file_path}")
                return

            # 读取Excel文件
            df = pd.read_excel(file_path)
            logger.info(f"Excel文件列名: {df.columns.tolist()}")
            logger.info(f"Excel文件形状: {df.shape}")

            # 根据你的描述，Excel文件结构是：
            # 第一列：敏感词类型
            # 第二列：敏感词
            # 第一行是表头

            if len(df.columns) < 2:
                logger.error("Excel文件至少需要两列：敏感词类型和敏感词")
                return

            # 使用列索引而不是列名，确保正确读取
            type_column = df.columns[0]  # 第一列：敏感词类型
            word_column = df.columns[1]  # 第二列：敏感词

            logger.info(f"敏感词类型列: {type_column}")
            logger.info(f"敏感词内容列: {word_column}")

            # 加载敏感词（跳过表头）
            loaded_count = 0
            for _, row in df.iterrows():
                word_type = str(row[type_column]).strip()
                word = str(row[word_column]).strip()

                # 跳过空值和无效数据
                if (
                    word
                    and word != "nan"
                    and word != "敏感词"
                    and word_type
                    and word_type != "nan"
                    and word_type != "敏感词类型"
                ):
                    self.sensitive_words[word] = word_type
                    loaded_count += 1

            logger.info(
                f"成功加载 {loaded_count} 个敏感词，总计 {len(self.sensitive_words)} 个唯一敏感词"
            )

        except Exception as e:
            logger.error(f"加载敏感词文件失败: {e}")
            # 添加一些示例敏感词用于测试
            self.sensitive_words = {"测试敏感词": "测试类型", "示例": "示例类型"}

    def _build_automaton(self) -> None:
        """构建Aho-Corasick自动机"""
        try:
            self.automaton = ahocorasick.Automaton()

            for word in self.sensitive_words.keys():
                self.automaton.add_word(word, word)

            self.automaton.make_automaton()
            logger.info("Aho-Corasick自动机构建完成")

        except Exception as e:
            logger.error(f"构建自动机失败: {e}")
            self.automaton = None

    def _extract_page_info(self, content: str) -> Dict[int, int]:
        """提取页码信息，返回字符位置到页码的映射"""
        page_positions = {}
        current_page = 1

        # 查找页码标识，支持多种格式
        page_patterns = [
            r"<!--\s*page\s*:\s*(\d+)\s*-->",  # <!-- page:1 -->
            r"<!--\s*第\s*(\d+)\s*页\s*-->",  # <!-- 第1页 -->
            r"\[page\s*:\s*(\d+)\]",  # [page:1]
            r"第\s*(\d+)\s*页",  # 第1页
        ]

        for pattern in page_patterns:
            for match in re.finditer(pattern, content, re.IGNORECASE):
                page_num = int(match.group(1))
                page_positions[match.start()] = page_num
                current_page = max(current_page, page_num)

        # 如果没有找到页码标识，整个文档视为第1页
        if not page_positions:
            page_positions[0] = 1

        return page_positions

    def _get_page_for_position(
        self, position: int, page_positions: Dict[int, int]
    ) -> int:
        """根据字符位置获取对应的页码"""
        if not page_positions:
            return 1

        # 找到小于等于当前位置的最大页码位置
        valid_positions = [pos for pos in page_positions.keys() if pos <= position]
        if not valid_positions:
            return 1

        max_position = max(valid_positions)
        return page_positions[max_position]

    def detect_sensitive_words(
        self, content: str, use_cache: bool = True
    ) -> List[SensitiveWordResult]:
        """检测敏感词并返回结果"""
        # 检查缓存
        if use_cache:
            cached_result = detection_cache.get(content)
            if cached_result is not None:
                logger.debug("使用缓存结果")
                return cached_result

        if not self.automaton:
            logger.warning("自动机未初始化，使用正则表达式检测")
            results = self._detect_with_regex(content)
        else:
            results = self._detect_with_automaton(content)

        # 缓存结果
        if use_cache:
            detection_cache.set(content, results, self.settings.cache_expire_seconds)

        return results

    def _detect_with_automaton(self, content: str) -> List[SensitiveWordResult]:
        """使用Aho-Corasick算法检测敏感词"""

        # 提取页码信息
        page_positions = self._extract_page_info(content)

        # 使用Aho-Corasick算法检测
        found_words = defaultdict(list)  # 敏感词 -> [位置列表]

        for end_index, word in self.automaton.iter(content):
            start_index = end_index - len(word) + 1
            found_words[word].append(start_index)

        # 生成结果
        results = []
        序号 = 1

        for word, positions in found_words.items():
            word_type = self.sensitive_words.get(word, "未分类")
            count = len(positions)

            # 获取所有出现位置的页码
            pages = set()
            for pos in positions:
                page = self._get_page_for_position(pos, page_positions)
                pages.add(page)

            # 格式化页码信息
            if len(pages) == 1:
                page_info = f"第{list(pages)[0]}页"
            else:
                sorted_pages = sorted(pages)
                page_info = f"第{','.join(map(str, sorted_pages))}页"

            result = SensitiveWordResult(
                序号=序号,
                敏感词类型=word_type,
                敏感词内容=word,
                出现次数=count,
                对应页码=page_info,
            )
            results.append(result)
            序号 += 1

        return results

    def _detect_with_regex(self, content: str) -> List[SensitiveWordResult]:
        """使用正则表达式进行检测（备用方法）"""
        page_positions = self._extract_page_info(content)
        found_words = defaultdict(list)

        for word in self.sensitive_words.keys():
            # 使用词边界确保精确匹配
            pattern = r"\b" + re.escape(word) + r"\b"
            for match in re.finditer(pattern, content, re.IGNORECASE):
                found_words[word].append(match.start())

        # 生成结果（与上面的逻辑相同）
        results = []
        序号 = 1

        for word, positions in found_words.items():
            word_type = self.sensitive_words.get(word, "未分类")
            count = len(positions)

            pages = set()
            for pos in positions:
                page = self._get_page_for_position(pos, page_positions)
                pages.add(page)

            if len(pages) == 1:
                page_info = f"第{list(pages)[0]}页"
            else:
                sorted_pages = sorted(pages)
                page_info = f"第{','.join(map(str, sorted_pages))}页"

            result = SensitiveWordResult(
                序号=序号,
                敏感词类型=word_type,
                敏感词内容=word,
                出现次数=count,
                对应页码=page_info,
            )
            results.append(result)
            序号 += 1

        return results

    def generate_markdown_table(self, results: List[SensitiveWordResult]) -> str:
        """生成markdown格式的结果表格"""
        if not results:
            return "| 序号 | 敏感词类型 | 敏感词内容 | 出现次数 | 对应页码 |\n|------|------------|------------|----------|----------|\n| - | 未检测到敏感词 | - | - | - |"

        # 表头
        table = "| 序号 | 敏感词类型 | 敏感词内容 | 出现次数 | 对应页码 |\n"
        table += "|------|------------|------------|----------|----------|\n"

        # 数据行
        for result in results:
            table += f"| {result.序号} | {result.敏感词类型} | {result.敏感词内容} | {result.出现次数} | {result.对应页码} |\n"

        return table

    def reload_sensitive_words(self) -> bool:
        """重新加载敏感词列表"""
        try:
            self._load_sensitive_words()
            self._build_automaton()
            logger.info("敏感词列表重新加载成功")
            return True
        except Exception as e:
            logger.error(f"重新加载敏感词列表失败: {e}")
            return False
