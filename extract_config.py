#!/usr/bin/env python3
"""
配置版本：直接修改这里的字段名
"""
import json
import sys

# 配置区域 - 修改这里的字段名
FIELD_NAME = "arg1"  # 改成你需要的字段名，比如 "content", "data", "result" 等

def extract_table(json_str):
    """提取表格"""
    try:
        # 解析外层JSON
        data = json.loads(json_str)
        
        # 获取配置的字段
        if FIELD_NAME in data:
            inner_json = json.loads(data[FIELD_NAME])
            if 'markdown_table' in inner_json:
                return inner_json['markdown_table'].replace('\\n', '\n')
        
        return f"错误: 未找到字段 '{FIELD_NAME}' 或其中的 'markdown_table'"
        
    except Exception as e:
        return f"错误: {e}"

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(f"使用方法: python extract_config.py \"JSON字符串\"")
        print(f"当前配置的字段名: {FIELD_NAME}")
        print("如需修改字段名，请编辑脚本中的 FIELD_NAME 变量")
        sys.exit(1)
    
    result = extract_table(sys.argv[1])
    print(result)
