#!/usr/bin/env python3
"""
提取敏感词检测结果中的markdown表格内容
"""
import json
import sys
import argparse


def extract_markdown_table(json_result):
    """
    从JSON结果中提取markdown表格内容
    
    Args:
        json_result (str or dict): JSON格式的检测结果
        
    Returns:
        str: 格式化的markdown表格内容
    """
    try:
        # 如果输入是字符串，解析为字典
        if isinstance(json_result, str):
            data = json.loads(json_result)
        else:
            data = json_result
        
        # 提取markdown_table字段
        if 'markdown_table' not in data:
            return "错误: 未找到markdown_table字段"
        
        markdown_table = data['markdown_table']
        
        # 将\n转换为实际的换行符
        formatted_table = markdown_table.replace('\\n', '\n')
        
        return formatted_table
        
    except json.JSONDecodeError as e:
        return f"JSON解析错误: {e}"
    except Exception as e:
        return f"处理错误: {e}"


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='提取敏感词检测结果中的markdown表格')
    parser.add_argument('json_input', nargs='?', help='JSON格式的检测结果（可选，如果不提供则从stdin读取）')
    parser.add_argument('-f', '--file', help='从文件读取JSON数据')
    parser.add_argument('-o', '--output', help='输出到文件（可选，默认输出到stdout）')
    
    args = parser.parse_args()
    
    # 获取输入数据
    json_data = None
    
    if args.file:
        # 从文件读取
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                json_data = f.read()
        except FileNotFoundError:
            print(f"错误: 文件 {args.file} 不存在", file=sys.stderr)
            sys.exit(1)
        except Exception as e:
            print(f"读取文件错误: {e}", file=sys.stderr)
            sys.exit(1)
    elif args.json_input:
        # 从命令行参数读取
        json_data = args.json_input
    else:
        # 从stdin读取
        json_data = sys.stdin.read()
    
    if not json_data.strip():
        print("错误: 没有提供输入数据", file=sys.stderr)
        sys.exit(1)
    
    # 提取markdown表格
    result = extract_markdown_table(json_data)
    
    # 输出结果
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(result)
            print(f"结果已保存到: {args.output}")
        except Exception as e:
            print(f"写入文件错误: {e}", file=sys.stderr)
            sys.exit(1)
    else:
        print(result)


if __name__ == "__main__":
    main()
