#!/usr/bin/env python3
"""
简单版本：提取markdown表格内容
使用方法: python extract_table_simple.py "JSON字符串"
"""
import json
import sys


def extract_table(json_str):
    """提取并格式化markdown表格"""
    try:
        data = json.loads(json_str)
        markdown_table = data.get('markdown_table', '')
        # 将\n转换为实际换行符
        return markdown_table.replace('\\n', '\n')
    except Exception as e:
        return f"错误: {e}"


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("使用方法: python extract_table_simple.py \"JSON字符串\"")
        print("示例: python extract_table_simple.py '{\"markdown_table\": \"表格内容\"}'")
        sys.exit(1)
    
    result = extract_table(sys.argv[1])
    print(result)
